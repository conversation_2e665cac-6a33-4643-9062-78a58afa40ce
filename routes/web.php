<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\HomeController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/
Route::get('/dashboard', function () {
    return view('homepage');
})->middleware(['auth'])->name('dashboard');

Route::get('/contact/1', function () {
    return view('contact')->with(
        ['pages' => App\Models\ArticlePage::orderBy('order')->get()]
    );
});
Route::get('/contact', function () {
    return view('contact')->with(
        ['pages' => App\Models\ArticlePage::orderBy('order')->get()]
    );
});

Route::get('/', function () {
    return redirect('/category/1-jsgm');
});

//Route::get('/category/1-jsgm/1-homepage', function () {return view('homepage')->with(
//    ['pages' => App\Models\ArticlePage::orderBy('order')->get()]
//);});
Route::get('/category/1-jsgm/3-for-authors', function () {
    return view('3-for-authors')->with(
        [
            'pages' => App\Models\Page::orderBy('order')->get(),
            'articlePages' => App\Models\ArticlePage::orderBy('order')->get()
        ]
    );
});
Route::get('/category/1-jsgm/4-aims26scope', function () {
    return view('4-aims26scope')->with(
        [
            'pages' => App\Models\Page::orderBy('order')->get(),
            'articlePages' => App\Models\ArticlePage::orderBy('order')->get()
        ]
    );
});
Route::get('/category/1-jsgm/5-ethical-principles-for-jgsm', function () {
    return view('5-ethical-principles-for-jgsm')->with(
        [
            'pages' => App\Models\Page::orderBy('order')->get(),
            'articlePages' => App\Models\ArticlePage::orderBy('order')->get()
        ]
    );
});
Route::get('/category/1-jsgm/6-advisory-board', function () {
    return view('6-advisory-board')->with(
        [
            'pages' => App\Models\Page::orderBy('order')->get(),
            'articlePages' => App\Models\ArticlePage::orderBy('order')->get()
        ]
    );
});
Route::get('/category/1-jsgm/7-abstracting---indexing', function () {
    return view('7-abstracting---indexing')->with(
        [
            'pages' => App\Models\Page::orderBy('order')->get(),
            'articlePages' => App\Models\ArticlePage::orderBy('order')->get()
        ]
    );
});
Route::get('/category/1-jsgm/27-editorial-board', function () {
    return view('27-editorial-board')->with(
        [
            'pages' => App\Models\Page::orderBy('order')->get(),
            'articlePages' => App\Models\ArticlePage::orderBy('order')->get()
        ]
    );
});
Route::get('/category/1-jsgm/56-about-the-journal', function () {
    return view('56-about-the-journal')->with(
        [
            'pages' => App\Models\Page::orderBy('order')->get(),
            'articlePages' => App\Models\ArticlePage::orderBy('order')->get()
        ]
    );
});

Route::get('/category/1-jsgm', [HomeController::class, 'index']);
Route::get('/category/1-jsgm/{slug}', [HomeController::class, 'ArticlePage']);
Route::get('/category/1-jsgm/{slug}/{slug2}', [HomeController::class, 'Article']);

Route::get('/download/{pdfid}/{id}', [HomeController::class, 'downloadCount']);
Route::get('/file/{id}/download', [HomeController::class, 'FileRedirects']);

require __DIR__ . '/auth.php';

//Route::get('category/1-jsgm/{slug}', [HomeController::class, 'slug'])->where('slug', '^((?!panel|nova|.png|.jpg|.gif|.jpeg).)*$');


Route::get('sitemap.xml', [HomeController::class, 'sitemap']);
