<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFileRedirectsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('file_redirects', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('page_id');
            $table->text('name');
            $table->text('file_name');
            $table->text('file');
            $table->unsignedInteger('status');
            $table->unsignedInteger('download_count');
            $table->unsignedInteger('row');
            $table->dateTime('systime');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('file_redirects');
    }
}
