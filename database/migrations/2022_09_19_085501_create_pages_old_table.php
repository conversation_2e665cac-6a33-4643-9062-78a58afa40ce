<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePagesOldTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('pages_old', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('category_id')->nullable();
            $table->unsignedInteger('home')->nullable();
            $table->unsignedInteger('row')->nullable();
            $table->unsignedInteger('status')->nullable();
            $table->unsignedInteger('home_category')->nullable();
            $table->unsignedInteger('slider')->nullable();
            $table->unsignedInteger('inner_slider')->nullable();
            $table->unsignedInteger('ad')->nullable();
            $table->unsignedInteger('news')->nullable();
            $table->string('name')->nullable();
            $table->string('name_eng')->nullable();
            $table->text('content')->nullable();
            $table->text('content_eng')->nullable();
            $table->dateTime('systime')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('pages_old');
    }
}
