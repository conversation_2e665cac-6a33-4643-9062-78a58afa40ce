<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateArticlePagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('article_pages', function (Blueprint $table) {
            $table->id();
            $table->text('menu_name');
            $table->string('title')->nullable();
            $table->text('content')->nullable();
            $table->integer('order',)->default(999)->nullable();
            $table->integer('isactive')->nullable();
            $table->integer('ispublish')->nullable();
            $table->string('seo_title')->nullable();
            $table->string('about')->nullable();
            $table->text('meta_keywords')->nullable();
            $table->string('meta_description')->nullable();
            $table->string('slug')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('article_pages');
    }
}
