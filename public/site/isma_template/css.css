




#profile-description {
    width: 562px; position:relative;
}
#profile-description .text {
    width: 527px; margin:0 15px 2px 20px; position:relative; font-family: Arial; font-size: 14px; display: block;
}
#profile-description .show-more {
    width: 562px; color: #777; font-family:tahoma; position:relative; font-size: 12px; font-weight:bold;  padding-top: 0px; height: 13px; text-align: center; background: #f1f1f1; cursor: pointer;
}
#profile-description .show-more:hover { 
    color: #1779dd;
}
#profile-description .show-more-height { height: 252px; overflow:hidden; }

.renklendirme1{
background-color:white;
color:black;
font-family:lucida sans unicode;
font-size:10pt;
cursor:pointer;
}
.renklendirme1:Hover {
background-color:#FF7700;
color:red;
font-family:lucida sans unicode;
font-size:10pt;
cursor:pointer;
}

.mailingbirlesik1
{
margin-top: 3px;
line-height:16px;
margin-bottom: 3px;
}
.mailingbirlesik2
{
margin-top: 1px;
line-height:14px;
margin-bottom: 1px;
}


 
 .xxxx
{
line-height: 85%;
font-size:7pt;
}   
 
.mailingbirlesik3
{
margin-top: 0px;
line-height:11px;
margin-bottom: 0px;
}

.areaetiketler {
width:450px;
height:420px;
font-family:lucida sans unicode;
font-size:9pt;
color:black;
border:1px solid;
border-color:#c0c0c0;
}


.xinput {
    border:2px solid #dadada;
    border-radius:7px;
    font-size:20px;
    padding:5px; 
}

.xinput:focus { 
    outline:none;
    border-color:#9ecaed;
    box-shadow:0 0 10px #9ecaed;
}

.selectsehir
{
background: transparent;
width: 197px;
height: 28px;
line-height: 28px;
border:0;
font-size:14pt;
vertical-align: middle;
font-family: lucida sans unicode;
}

.sepetselect
{

width:44px;
height: 20px;
line-height: 28px;
vertical-align:center;
font-size:10pt;
font-family: verdana;
}




.tabinactive 
{
color:red; 
background-color:blue;
}
.tabactive 
{
color:blue; 
background-color:yellow;
}

.ustmenuler
{
float: left;
background-color:#FF7800;
color: white;
cursor: pointer;
height:36px;
font-family:tahoma;
font-size:10pt;
font-weight:bold;
}
.ustmenuler:Hover
{
float: left;
background-color:#FFDE00;
color: black;
cursor: pointer;
height:36px;
font-family:tahoma;
font-size:10pt;
font-weight:bold;
}
.bilgimenu
{
float: left;
color: white;
cursor: pointer;
height:28px;
font-family:helvetica;
font-size:11pt;
}
.bilgimenu:Hover
{
float: left;
background-color:#F6F6F6;
color: black;
cursor: pointer;
height:28px;
font-family:helvetica;
font-size:11pt;
}





.bilgimenubilgi
{
background-color:#F6F6F6;
float: left;
color: black;
cursor: pointer;
height:28px;
font-family:helvetica;
font-size:11pt;
}




.genel10
{
font-family:lucida sans unicode;
font-size:10pt
}






.inputgiris{
width: 150px;
height: 22px;
border-style:solid;
border-color:#959595;
border-width:0px;
}
.selectgiris
{

width: 160px;
height: 25px;
line-height: 25px;
font-size:11pt;
vertical-align: middle;
font-family: helvetica;
background-color:white;
}
.selectuyeorta
{
width: 200px;
height: 25px;
line-height: 25px;
font-size:11pt;
vertical-align: middle;
font-family: lucida sans unicode;
color:#555555;
background-color:white;
}

.selectuyedogum
{
width:75px;
height: 25px;
line-height: 25px;
font-size:11pt;
vertical-align: middle;
font-family: lucida sans unicode;
color:#555555;
background-color:white;
}






.yazibuton{
white-space: normal;
}

.satisbilgiinput{
width: 80px;
}

.inputteslimatadresi
{
padding-left: 5px;
width: 380px;
height: 33px;
line-height:31px;
border: 0;
}





.frameap{
background-repeat:no-repeat;
}


.em1
{
line-height: 1em;
}
.digerap {
background-size:179px 126px;
background-repeat:no-repeat;
padding-top:0px;
padding-top:0px;
}

.ustfh {
position:fixed;
top:0;
left:0;
width:100%;
}



.selectorta
{
background: transparent;
width: 197px;
height: 28px;
line-height: 28px;
border:0;
font-size:14pt;
vertical-align: middle;
font-family: lucida sans unicode;

}


.anabg {
display:block;
width:410;
height:290;
position:absolute;
top:0;
left:0;
z-index:0;
background-repeat:no-repeat;
 }


.yetkilikullaniciadisifreinput{
width:240px;
height:30px;
line-height: 30px;
font-family:lucida sans unicode;
font-size:10pt;
color:#707070;
font-weight:bold;
border:1;
background-color:#EAE9EE;
}
.yetkilionaykoduinput{
width:80px;
height:21px;
line-height: 21px;
font-family:lucida sans unicode;
font-size:8pt;
color:#707070;
border:1;
background-color:#EAE9EE;
}

.areatedarikciadres {
height:60px;
width:350px;
font-family:lucida sans unicode;
font-size:9pt;
color:black;
border:1px solid;
border-color:#c0c0c0;
}

.areatedarikcinotlar {
height:150px;
width:400px;
font-family:lucida sans unicode;
font-size:9pt;
color:black;
border:1px solid;
border-color:#c0c0c0;
}






.areaeditor {
height:350px;
width:750px;
font-family:lucida sans unicode;
font-size:9pt;
color:black;
border:1px solid;
border-color:#c0c0c0;
}

.areasatiseditor {
height:200px;
width:650px;
font-family:tahoma;
font-size:9pt;
color:black;
border:1px solid;
border-color:#c0c0c0;
}



.areaad {
height:25px;
width:650px;
font-family:tahoma;
font-size:9pt;
color:black;
border:1px solid;
border-color:#c0c0c0;
}


.areanasilkullanilir{
height:120px;
width:675px;
font-family:lucida sans unicode;
font-size:9pt;
color:black;
border:1px solid;
border-color:#c0c0c0;
}


.areaxmlbasligi{
height:130px;
width:500px;
font-family:lucida sans unicode;
font-size:9pt;
color:black;
border:1px solid;
border-color:#c0c0c0;
}


.areaadres{
height:120px;
width:475px;
font-family:lucida sans unicode;
font-size:9pt;
color:black;
border:1px solid;
border-color:#c0c0c0;
}



.inputsayfaadbaslik{
width:200px;
height:22px;
line-height: 22px;
font-family:lucida sans unicode;
font-size:10pt;
color:black;
border:1;
}


.inputuyelik{
width:347px;
height:28px;
line-height: 28px;
font-family:lucida sans unicode;
font-size:10pt;
color:black;
border:0;

}

.inputaciliraktivasyon{
width:240px;
height:24px;
line-height: 22px;
font-family:lucida sans unicode;
font-size:10pt;
color:black;
}

.butonaciliraktivasyon{
height:30px;
width:170px;
}



.inputaktivasyon{
width:250px;
height:22px;
line-height: 22px;
font-family:lucida sans unicode;
font-size:10pt;
color:black;
border:0;
}
.butonaktivasyon{
height:24px;
width:70px;
}

.butongenel{
height:24px;
}



.inputuyelik2{
width:174px;
height:24px;
line-height: 22px;
font-family:lucida sans unicode;
font-size:10pt;
color:black;
border:0;
}






.inputdogumtarihi1{
width:116px;
height:20px;
line-height: 20px;
font-family:lucida sans unicode;
font-size:10pt;
color:black;
border:0;
}

.inputdogumtarihi2{
width:115px;
height:20px;
line-height: 20px;
font-family:lucida sans unicode;
font-size:10pt;
color:black;
border:0;
}



.inputresimkategori{
width:150px;
height:24px;
line-height: 22px;
font-family:lucida sans unicode;
font-size:10pt;
color:black;
border:1;
}


.inputtedarikci{
width:200px;
height:24px;
line-height: 24px;
font-family:lucida sans unicode;
font-size:10pt;
color:black;
border:1;
}



.inputfirsatara{
font-family:lucida sans unicode;
font-size:10pt;
color:black;
height:26px;
line-height: 26px;
width:168px;
border:0;
font-weight:bold;
}






.inputuyegirisi{
width:180px;
height:19px;
line-height: 19px;
font-family:lucida sans unicode;
font-size:9pt;
color:black;

}



.inputustsageposta{
width:147px;
height:16px;
line-height: 16px;
font-family:lucida sans unicode;
font-size:9pt;
color:black;

}

.inputeposta{
width:130px;
height:18px;
line-height: 18px;
font-family:lucida sans unicode;
font-size:9pt;
color:black;
border:0;
}

.inputsifre{
width:95px;
height:18px;
line-height: 18px;
font-family:lucida sans unicode;
font-size:9pt;
color:black;
border:0;
}

.inputalteposta{
width:215px;
height:19px;
line-height: 23px;
font-family:lucida sans unicode;
font-size:9pt;
color:black;
border:0;
background:transparent;
}



P.genel8{
font-family:lucida sans unicode;
font-size:8pt;
color:black;
}

P.genel9{
font-family:lucida sans unicode;
font-size:9pt;
color:black;
}






P.gerisayim{
font-family:lucida sans unicode;
font-size:10pt;
color:#878899;
font-weight:bold;
}






P.ustp{
font-family:lucida sans unicode;
font-size:8pt;
color:black;
}

A.ustlinkh {
font-size:8pt;
color:black;
font-family:lucida sans unicode;
text-decoration:none;
}
A.ustlinkh:Hover {
font-size:8pt;
color:black;
font-family:lucida sans unicode;
text-decoration:underline;
}


P.genel11{
font-family:lucida sans unicode;
font-size:11pt;
color:black;
}
P.verdana11{
font-family:verdana;
font-size:11pt;
color:black;
}
A.genel10h {
font-size:10pt;
color:blue;
font-family:lucida sans unicode;
text-decoration:none;
}
A.genel10h:Hover {
font-size:10pt;
color:blue;
font-family:lucida sans unicode;
text-decoration:underline;
}
A.genel10alticizili {
font-size:10pt;
color:blue;
font-family:lucida sans unicode;
}


A.genel10siyahkirmizi {
font-size:10pt;
color:black;
font-family:lucida sans unicode;
text-decoration:none;
}

A.genel10siyahkirmizi:Hover {
font-size:10pt;
color:red;
font-family:lucida sans unicode;
text-decoration:none;
}

A.genel10kirmizisiyah {
font-size:10pt;
color:red;
font-family:lucida sans unicode;
text-decoration:none;
}

A.genel10kirmizisiyah:Hover {
font-size:10pt;
color:black;
font-family:lucida sans unicode;
text-decoration:none;
}



A.menulink {
font-size:10pt;
color:blue;
font-family:lucida sans unicode;
text-decoration:none;
}
A.menulink:Hover {
font-size:10pt;
color:blue;
font-family:lucida sans unicode;
text-decoration:underline;
}
A.menulink2 {
font-size:10pt;
color:blue;
font-family:lucida sans unicode;
text-decoration:none;
}
A.menulink2:Hover {
font-size:10pt;
color:blue;
font-family:lucida sans unicode;
text-decoration:underline;
}
A.menulink3 {
font-size:10pt;
color:blue;
font-family:lucida sans unicode;
text-decoration:none;
}
A.menulink3:Hover {
font-size:10pt;
color:blue;
font-family:lucida sans unicode;
text-decoration:underline;
}

A.genel10hkirmizi {
font-size:10pt;
color:red;
font-family:lucida sans unicode;
text-decoration:none;
}


.asd
	{	
		width:180px;
		padding-left: 20px;
		margin:0px;
		background: url(acilirsol.html) ;
		height:24px;
		line-height:22px;
		font-family:lucida sans unicode;
		font-size:11px;
		text-decoration:none;
		color:black;

	}


	/*"""""""" (MAIN) Hover State""""""""*/	
.asd:hover	
	{	

		color:red;
		margin:0px;
	}
